package com.kbao.kbcelms.controller.enterprise;

import com.github.pagehelper.PageInfo;import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.common.constants.ElmsConstant;
import com.kbao.kbcelms.dataTemplate.model.DataTemplateField;
import com.kbao.kbcelms.dataTemplate.service.DataTemplateService;
import com.kbao.kbcelms.formConfig.model.FormConfigField;import com.kbao.kbcelms.formConfig.service.FormConfigService;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseEditVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.AgentEnterpriseApiService;import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import io.swagger.annotations.Api;import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;


@RestController
@RequestMapping("/api/agentEnterprise")
@Api(tags = "企业客户API")
public class AgentEnterpriseController extends BaseController {
    @Autowired
    private FormConfigService formConfigService;
    @Autowired
    private AgentEnterpriseApiService agentEnterpriseApiService;

    @ApiOperation(value = "查询展示字段", notes = "查询展示字段")
    @PostMapping("/fields")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询展示字段")
    public Result<List<FormConfigField>> getShowFields() {
        List<FormConfigField> showFields = formConfigService.getFieldList(ElmsConstant.FORM_CONFIG_AGENT_ENTERPRISE);
        return Result.succeed(showFields, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "保存企业客户", notes = "保存企业客户")
    @PostMapping("/save")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询展示字段")
    public Result<GenAgentEnterprise> save(@RequestBody AgentEnterpriseEditVo enterprise) {
        GenAgentEnterprise result = agentEnterpriseApiService.saveAgentEnterprise(enterprise);
        return Result.succeed(result, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询企业客户列表", notes = "查询企业客户列表")
    @PostMapping("/list")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询企业客户列表")
    public Result<PageInfo<GenAgentEnterprise>> getList(@RequestBody PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        PageInfo<GenAgentEnterprise> list = agentEnterpriseApiService.getAgentEnterpriseList(pageRequest);
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询企业客户明细", notes = "查询企业客户明细")
    @PostMapping("/getById")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询企业客户明细")
    public Result<GenAgentEnterprise> getById(@RequestBody GenAgentEnterprise param) {
        GenAgentEnterprise enterprise = agentEnterpriseApiService.getById(param.getId());
        return Result.succeed(enterprise, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "删除企业客户", notes = "删除企业客户")
    @PostMapping("/delete")
    @LogAnnotation(module = "企业客户API", recordRequestParam = true, action = "查询", desc = "查询展示字段")
    public Result delete(@RequestBody GenAgentEnterprise enterprise) {
        agentEnterpriseApiService.delete(enterprise.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
}
