package com.kbao.kbcelms.controller.onlineproduct;

import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.onlineproduct.service.OnlineProductConfigApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/onlineProduct")
@Api(tags = "线上产品配置管理")
public class OnlineProductApiController  extends BaseController {

    @Autowired
    private OnlineProductConfigApiService onlineProductConfigApiService;


    /**
     * 根据企业客户ID获取可配置的险种类型
     */
    @GetMapping("/getInsuranceTypesByEnterprise/{enterpriseId}")
    @ApiOperation(value = "根据企业客户ID获取可配置的险种类型", notes = "通过企业客户ID获取企业行业分类，根据行业分类查询线上产品配置，返回可配置的险种类型编码数组")
    public Result<List<String>> getInsuranceTypesByEnterprise(
            @ApiParam(value = "企业客户ID", required = true)
            @PathVariable Integer enterpriseId) {
        try {
            List<String> result = onlineProductConfigApiService.getInsuranceTypesByEnterprise(enterpriseId);
            return Result.succeed(result, "查询成功");
        } catch (Exception e) {
            logger.error("根据企业客户ID获取险种类型失败", e);
            return Result.failed("查询失败：" + e.getMessage());
        }
    }

}
