package com.kbao.kbcelms.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.kbcelms.interceptor.ApiNoAuthInterceptor;import com.kbao.kbcelms.interceptor.ApiUserAuthInterceptor;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;import java.util.List;

//

/**
 * spring mvc配置类
 *
 * <AUTHOR>
 * @since V1.0.0
 */
@Configuration
public class WebMvcConfiguration extends WebMvcConfigurationSupport implements EnvironmentAware {

    /**
     * 需要拦截的请求,Ant表达式
     */
    private static String[] LOGIN_INCLUDE = {"/**"};

    /**
     * 验证token需要排除的请求,Ant表达式
     */
    private static String[] LOGIN_EXCLUDE = {
            "/doc.html",
            "/swagger-ui.html",
            "/swagger-ui.html/**",
            "/swagger-resources/**",
            "/webjars/**",
            "/v2/**",
            "/healthcheck",
            "/api/noauth/**",
            "/api/nontenant/**"
    };

    private static String[] WHITELIST_INCLUDE = {
            "/doc.html",
            "/swagger-ui.html",
            "/swagger-ui.html/**",
            "/swagger-resources/**",
            "/webjars/**",
            "/v2/**",
            "/healthcheck",
            "/api/nontenant/**"
    };

    /**
     * 拦截重定向url
     */
    private static final String SEND_URL = "/login.html";

    @Bean
    public ApiUserAuthInterceptor userAuthInterceptorAdapter(){
        return new ApiUserAuthInterceptor();
    }

    @Bean
    public ApiNoAuthInterceptor noAuthInterceptor() {
        return new ApiNoAuthInterceptor();
    }

    /**
     * 拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(noAuthInterceptor()).addPathPatterns(LOGIN_INCLUDE).excludePathPatterns(WHITELIST_INCLUDE).order(1);
        registry.addInterceptor(userAuthInterceptorAdapter()).addPathPatterns(LOGIN_INCLUDE).excludePathPatterns(LOGIN_EXCLUDE).order(2);
    }

    /**
     * 静态资源
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");

    }


    /**
     * 过滤器
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean corsFilterRegistrationBean() {
        // 对响应头进行CORS授权
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.setAllowedOriginPatterns(Collections.singletonList(CorsConfiguration.ALL));
        corsConfiguration.addAllowedHeader(CorsConfiguration.ALL);
        corsConfiguration.addAllowedMethod(CorsConfiguration.ALL);
        corsConfiguration.setAllowCredentials(true);
        corsConfiguration.setMaxAge(3600L);

        // 注册CORS过滤器
        UrlBasedCorsConfigurationSource configurationSource = new UrlBasedCorsConfigurationSource();
        configurationSource.registerCorsConfiguration("/**", corsConfiguration);
        CorsFilter corsFilter = new CorsFilter(configurationSource);
        return new FilterRegistrationBean(corsFilter);
    }

    /**
     * 注册跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**");
    }


    @Override
    public void setEnvironment(Environment environment) {
    }

    /**
     * ResponseBody 消息转换方式
     */
    @Override
    protected void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        super.configureMessageConverters(converters);
        // 支持字符串格式转换
        converters.add(new StringHttpMessageConverter(Charset.forName("utf-8")));
        // 支持json格式转换
        converters.add(getMappingJackson2HttpMessageConverter());
    }

    @Autowired
    private ObjectMapper objectMapper;


    public MappingJackson2HttpMessageConverter getMappingJackson2HttpMessageConverter() {
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        //设置日期格式
//        ObjectMapper objectMapper = new ObjectMapper();
//        SimpleDateFormat smt = new SimpleDateFormat("yyyy-MM-dd");
//        objectMapper.setDateFormat(smt);
//        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        mappingJackson2HttpMessageConverter.setObjectMapper(objectMapper);
        //设置中文编码格式
        List<MediaType> list = new ArrayList<MediaType>();
        list.add(MediaType.APPLICATION_JSON_UTF8);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(list);
        return mappingJackson2HttpMessageConverter;
    }

}
