<template>
  <div class="division-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>
      <el-table-column align="center" prop="name" label="分工项目名称"></el-table-column>
      <el-table-column align="center" label="比例上限">
        <template slot-scope="scope">{{ scope.row.ratio }}%</template>
      </el-table-column>
      <el-table-column align="center" prop="number" label="可指定人数上限"></el-table-column>
      <el-table-column align="center" label="状态" width="100">
        <template slot-scope="scope">{{ statusList | getStatus(scope.row.status) }}</template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="update(scope.row)">编辑</el-button>
          <el-button class="btn-center" type="text" @click="del(scope.row)" v-if="scope.row.status =='0'">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

    <!-- 删除 -->
    <DtPopup :isShow.sync="showDelPopup" @close="closeDelPopup" title="提示" center :footer="false" width="30%">
      <div class="check-popup">
        <div style="text-align: center">请确认是否删除？</div>
        <div class="btn-wrap">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'120px' }"
                     @click="closeDelPopup">取消</el-button>
          <el-button type="primary" class="btn-width"
                     :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color,width:'120px' }"
                     @click="delConfirm">确认</el-button>
        </div>
      </div>
    </DtPopup>

    <!-- 新增/编辑 -->
    <DtPopup :isShow.sync="showPopup" @close="closePopup" size="mini" :title="this.isAdd?'新增分工项目':'编辑分工项目'" :footer="false">
      <el-form ref="addForm" :model="addData" label-width="120px" :rules="addRules" label-position="left">
        <div style="max-height:70vh;overflow:auto">
          <el-form-item label="分工名称" prop="name">
            <el-input v-model="addData.name" auto-complete="off" class="dt-input-width" placeholder="请输入分工名称" required></el-input>
          </el-form-item>
          <el-form-item label="比例上限" prop="ratio">
            <el-input v-model="addData.ratio" auto-complete="off" class="dt1-input-width" style="width: 180px;" placeholder="请输入比例上限" required></el-input> %
          </el-form-item>
          <el-form-item label="可指定人数上限" prop="number">
            <el-input v-model="addData.number" auto-complete="off" class="dt1-input-width" style="width: 180px;" placeholder="请输入可指定人数上限" required></el-input> 人
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio v-model="addData.status" v-for="(item, index) in statusList" :key="index" :label="item.dicItemCode">{{ item.dicItemName }}</el-radio>
          </el-form-item>
        </div>
        <div style="padding:20px 0;text-align: center">
          <el-button type="primary" class="btn-width"
                     :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px',width:'120px' }"
                     @click="closePopup">取消</el-button>
          <el-button type="primary" class="btn-width"
                     :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color,width:'120px' }"
                     @click="confirm">确认</el-button>
        </div>
      </el-form>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/division/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";

export default {
  name: "divisionRatioList",
  data() {
    return {
      toolListProps: {
        toolTitle: "分工比例管理",
        toolList: [
          {
            name: "新增分工项目",
            icon: "iconfont icondt8",
            // btnCode: "elms:role:add"
          }
        ]
      },
      tableData: [{}],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          status: ""
        }
      },
      searchFormTemp: [
        {
          label: "状态",
          name: "status",
          type: "select",
          width: "200px",
          list:[]
        }
      ],
      total: 0,
      showPopup: false,
      isAdd:true,
      addData: {
        id:"",
        name: "",
        ratio: "",
        number: "",
        status: "1" // 默认启用
      },
      addRules: {
        name: [{required: true, validator: validate, trigger: "blur"}],
        ratio: [{
          required: true, validator: validate, trigger: "blur",
          regax: [
            {message: "请输入数字", ruleFormat: "/^\\d+$/"}
          ]
        }],
        number: [{
          required: true, validator: validate, trigger: "blur", regax: [
            {message: "请输入数字", ruleFormat: "/^\\d+$/"}
          ]
        }],
      },
      statusList:[{dicItemCode:"0",dicItemName:"停用"},{dicItemCode:"1",dicItemName:"启用"}],
      showDelPopup : false,
      deleteDivisionId:""
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    }
  },
  filters: {
    getStatus(list, status) {
      let index = list.findIndex((v) => v.dicItemCode == status);
      return index > -1 ? list[index].dicItemName : "";
    }
  },
  async created() {
    this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {
      this.searchFormTemp[0].list = this.statusList;
    },
    async initData() {
      let res = await api.getDivisionPage(this.initParam);
      if (res) {
        this.total = res.total;
        this.tableData = [];
        if (res.list) {
          this.tableData = res.list ? res.list : [{}];
        }
      }
    },
    handleTool(item) {
      if (item.name == "新增分工项目") {
        this.isAdd = true;
        this.showPopup = true;
      }
    },
    closePopup() {
      this.addData = _.cloneDeep(this.$options.data().addData);
      this.showPopup = false;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      });
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    update(row) {
      this.isAdd = false;
      this.addData.id = row.id;
      this.addData.name = row.name;
      this.addData.number = row.number;
      this.addData.ratio = row.ratio;
      this.addData.status = row.status;
      this.showPopup = true;
    },
    async confirm() {
      if (!validateAlls(this.$refs.addForm)) {
        return;
      }

      if (this.addData.id && this.addData.id != '') {
        let res = await api.updateDivision(this.addData);
        if (res) {
          this.$message({
            type: "success",
            message: "修改成功"
          });
        }
      } else {
        let res = await api.saveDivision(this.addData);
        if (res) {
          this.$message({
            type: "success",
            message: "新增成功"
          });
          this.initData();
          this.showPopup = false;
        }
      }
    },
    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },
    del(row){
      this.deleteDivisionId = row.id;
      this.showDelPopup = true;
    },
    closeDelPopup(){
      this.deleteDivisionId = "";
      this.showDelPopup = false;
    },
    async delConfirm() {
      let res = await api.deleteDivision({id: this.deleteDivisionId});
      if (res) {
        this.$message({
          type: "success",
          message: "删除成功"
        });
      }
      this.initData();
      this.deleteDivisionId = "";
      this.showDelPopup = false;
    }
  }
};
</script>
<style lang="less">
.division-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }
  }

  .dt1-input-width {
    width: 50%;
  }

}

.check-popup {
  width: 100%;

  .btn-wrap {
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center;

    .btn-width {
      width: 100px;
    }
  }

  .end-exam-text {
    margin-top: 10px;
  }
}

</style>
