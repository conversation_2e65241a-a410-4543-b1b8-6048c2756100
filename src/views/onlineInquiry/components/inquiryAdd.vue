<template>
  <div class="inquiry-add">
    <el-form ref="addForm" :model="addData" :rules="addRules" label-position="right">
      <div class="info-item">
        <div class="item">
            <div class="item-rule"><span>*</span>投保人</div>
<!--          <el-form-item prop="policyholder">-->
<!--            <template slot="label">-->
<!--              <span style="color: red">*</span>投保人-->
<!--            </template>-->
            <el-select v-model="addData.policyholder" placeholder="请选择投保人" class="dt-input-width">
              <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                :label="template.dicItemName" :value="template.dicItemName" />
            </el-select>
<!--          </el-form-item>-->
        </div>
        <div class="item">
<!--          <el-form-item prop="insured">-->
<!--            <template slot="label">-->
<!--              <span class="item-rule-span">*</span>被保人-->
<!--            </template>-->
          <div class="item-rule"><span>*</span>被保人</div>
            <el-select v-model="addData.insured" placeholder="请选择被保人" class="dt-input-width">
              <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                :label="template.dicItemName" :value="template.dicItemName" />
            </el-select>
            <span style="margin-left: 20px;color: rgb(0, 153, 255)"><i class="el-icon-document-copy"
                style="padding-right: 6px;font-size: 16px"></i><el-button type="text"
                @click="copyPolicyholderInfo">同投保人</el-button></span>
<!--          </el-form-item>-->
        </div>
      </div>
      <div class="info-item">
        <div class="item">
<!--          <el-form-item label="社会统一信用代码" prop="policyholderCreditCode">-->
<!--            <template slot="label">-->
<!--              <span class="item-rule-span">*</span>社会统一信用代码-->
<!--            </template>-->
          <div class="item-rule"><span>*</span>社会统一信用代码</div>
            <el-input v-model="addData.policyholderCreditCode" auto-complete="off" class="dt-input-width"
              placeholder="社会统一信用代码"></el-input>
<!--          </el-form-item>-->
        </div>
        <div class="item">
<!--          <el-form-item label="社会统一信用代码" prop="insuredCreditCode">-->
<!--            <template slot="label">-->
<!--              <span class="item-rule-span">*</span>社会统一信用代码-->
<!--            </template>-->
            <div class="item-rule"><span>*</span>社会统一信用代码</div>
            <el-input v-model="addData.insuredCreditCode" auto-complete="off" class="dt-input-width"
              placeholder="社会统一信用代码"></el-input>
<!--          </el-form-item>-->
        </div>
      </div>
      <div class="info-item">
        <div class="item">
<!--          <el-form-item label="行业类型" prop="policyholderIndustryOne">-->
<!--            <template slot="label">-->
<!--              <span class="item-rule-span">*</span>行业类型-->
<!--            </template>-->
            <div class="item-rule"><span>*</span>行业类型</div>
            <div style="display:flex;">
              <div style="width: 40%">
                <el-select v-model="addData.policyholderIndustryOne" placeholder="请选择行业类型" class="full-width">
                  <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                    :label="template.dicItemName" :value="template.dicItemName" />
                </el-select>
              </div>
              <div style="margin-left: 20px;width: 40%">
                <el-select v-model="addData.policyholderIndustryTwo" placeholder="请选择行业类型" class="full-width">
                  <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                    :label="template.dicItemName" :value="template.dicItemName" />
                </el-select>
              </div>
            </div>
<!--          </el-form-item>-->
        </div>
        <div class="item">
<!--          <el-form-item label="行业类型" prop="insuredIndustryOne">-->
            <div class="item-rule"><span>*</span>行业类型</div>
<!--          <template slot="label">-->
<!--            <span class="item-rule-span">*</span>行业类型-->
<!--          </template>-->
            <div style="display:flex;">
              <div style="width: 40%">
                <el-select v-model="addData.insuredIndustryOne" placeholder="请选择行业类型" class="full-width">
                  <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                    :label="template.dicItemName" :value="template.dicItemName" />
                </el-select>
              </div>
              <div style="margin-left: 20px;width: 40%">
                <el-select v-model="addData.insuredIndustryTwo" placeholder="请选择行业类型" class="full-width">
                  <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                    :label="template.dicItemName" :value="template.dicItemName" />
                </el-select>
              </div>
            </div>
<!--          </el-form-item>-->
        </div>
      </div>

      <el-divider></el-divider>

      <div class="info-item">
        <div class="item">
<!--          <el-form-item label="平安业务员" prop="salesman">-->
<!--            <template slot="label">-->
<!--              <span class="item-rule-span">*</span>平安业务员-->
<!--            </template>-->
            <div class="item-rule"><span>*</span>平安业务员</div>
            <el-select v-model="addData.salesman" placeholder="请选择平安业务员" class="dt-input-width">
              <el-option v-for="template in inquiryCompanyList" :key="template.dicItemCode"
                :label="template.dicItemName" :value="template.dicItemName" />
            </el-select>
<!--          </el-form-item>-->
        </div>
      </div>

      <div class="info-item">
        <div class="item">
<!--          <el-form-item label="预计成交时间" prop="dealTime">-->
<!--            <template slot="label">-->
<!--              <span class="item-rule-span">*</span>预计成交时间-->
<!--            </template>-->
            <div class="item-rule"><span>*</span>预计成交时间</div>
            <el-date-picker v-model="addData.dealTime" type="date" placeholder="选择日期" class="dt-input-width"></el-date-picker>
<!--          </el-form-item>-->
        </div>
      </div>
      <div class="" style="margin-left: 70px">
        <div class="item">
          <el-table :data="addData.productData" class="dt-table" style="width: 85%" v-hover border stripe>
            <el-table-column align="center" label="产品大类">
              <template slot-scope="scope">
                <el-select v-model="scope.row.productCategoryCode" placeholder="请选择产品大类"
                  @change="changeCategory(scope.row)">
                  <el-option v-for="item in productCategoryList" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column align="center" label="产品名称">
              <template slot-scope="scope">
                <el-select v-model="scope.row.productCode" placeholder="请选择产品名称" @change="changeProduct(scope.row)">
                  <el-option v-for="item in productNameList" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="premium" label="预计保费">
              <template slot-scope="scope">
                <el-input v-model="scope.row.premium" style="width: 200px" type="number"
                  @blur="countPremium"></el-input>
                <span :style="{ 'padding-left': '10px', 'font-size': '20px', color: removeProductColor }"
                  @click="removeProduct(scope.row)"><i class="el-icon-remove"></i></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="item-premium" style="display: flex">
          <div class="item-button"><el-button type="primary" @click="addProduct">新增产品</el-button><i
              class="el-icon-question" style="color: #e1e10d;padding-left: 10px;" @click="showTips"></i></div>
          <div class="item-total-premium">预计总保费：<span style="color:#CC0000;">{{ addData.totalPremium }}</span> 万元</div>
        </div>
      </div>
      <div class="" style="padding: 20px 0;margin-left:70px;">
        <div class="item">询价描述:<i class="el-icon-document"></i><el-button type="text"
            @click="showMaterialPopup">询价资料参考</el-button></div>
      </div>
      <div style="margin-left:70px;">
        <el-input type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入询价描述" v-model="addData.notes"
          style="width: 85%"></el-input>
      </div>
      <div class="" style="display:flex;padding-left: 20px;margin-top: 20px;margin-left:70px;">
        <div style="margin-top: 8px;">上传附件</div>
        <div class="product-plan-config">
          <div class="head-item" v-if="productPlanList.length < 10">
            <div class="detail-upload-btn">
              <el-upload icon="el-icon-upload2" class="upload-demo downd_excedl" :multiple="false"
                style="display: inline-block; margin: 0 10px" :auto-upload="true" list-type="text"
                :data="detailFileParam" :show-file-list="false" :action="fileUploadUrl" :limit="10"
                :on-exceed="handleExceed" :before-upload="inquiryBeforeUpload" :on-success="uploadSuccess" :headers="{
                  access_token: currentLoginUser.access_token,
                  tenantId: currentLoginUser.tenantId,
                  funcId: currentLoginUser.funcId,
                }">
                <el-button type="primary">点击上传</el-button>
              </el-upload>
              <span
                style="font-size: 12px;color: #C7C7C7;">支持PDF/WORD/PPT/EXCEL/PNG/JPG/ZIP/RAR/7Z格式文件，最多10个，单个文件不大于20M</span>
            </div>

            <div class="materials-item" v-for="(item, index) in insuranceMaterials" :key="index">
              <div class="item">
                <span>{{ item.productName }}产品投保材料：{{ item.materials }}</span>
              </div>
            </div>

          </div>
        </div>
      </div>

      <div class="item-file">
        <div class="product-item" v-for="(item, index) in productPlanList" :key="index">
          <div style="width: 60%; padding-left:10px;padding-top: 15px;">
            <span>{{ item.fileName }}</span>
          </div>
          <div style="width: 35%;text-align: right;padding-top: 8px;">
            <i class="el-icon-delete"></i>
            <el-button type="text" @click="deleteProductPlanFile(item, index)" style="padding-left: 5px;">删除
            </el-button>
          </div>
        </div>
      </div>

      <el-divider></el-divider>

      <div class="foot-btn">
        <el-button type="primary" class="btn-width"
          :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"
          @click="temporarySave">暂存</el-button>
        <el-button type="primary" class="btn-width"
          :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }"
          @click="submitInquiry">提交</el-button>
      </div>
    </el-form>


    <!-- 询价资料参考 -->
    <DtPopup :isShow.sync="showPopup" @close="closePopup" size="mini" title="询价资料参考" :footer="false">
      <el-form ref="popupForm" :model="popupData" label-width="100px" label-position="right">
        <div class="inquiry-popup-category" style="display:flex;">
          <div class="item">产品大类</div>
          <div class="item-select">
            <el-select v-model="popupData.productCategoryCode" placeholder="请选择产品大类" @change="changeReferenceCategory">
              <el-option v-for="item in productCategoryList" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </div>
          <div class="item" style="margin-left: 30px;">产品名称</div>
          <div class="item-select">
            <el-select v-model="popupData.productCode" placeholder="请选择产品名称" @change="changeReferenceProduct">
              <el-option v-for="item in productNameList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>

        <div ref="productImage">

          <div class="inquiry-popup" style="padding-top: 10px;font-weight: 600;">
            <span>{{ popupData.productName }}</span>
          </div>

          <div style="border: 1px solid #DCDFE6">
            <div class="inquiry-popup">
              <div class="item">询价</div>
            </div>
            <div style="display:flex;" v-for="(item, index) in inquiryPopupList" :key="index">
              <div style="padding: 11px;width: 30%">
                <span>{{ item.name }}</span>
              </div>
              <div style="padding: 11px;width: 30%">
                <span>{{ item.value }}</span>
              </div>
            </div>
          </div>

          <div style="border: 1px solid #DCDFE6;margin-top: 20px">
            <div class="inquiry-popup">
              <div class="item">转投保</div>
            </div>

            <div style="display:flex;" v-for="(item, index) in inquiryInsureList" :key="index">
              <div style="padding: 11px;width: 30%">
                <span>{{ item.name }}</span>
              </div>
              <div style="padding: 11px;width: 30%">
                <span>{{ item.value }}</span>
              </div>
            </div>
          </div>
        </div>

        <div style="padding:20px 0;text-align: center">
          <el-button type="primary" class="btn-width"
            :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"
            @click="closePopup">取消</el-button>
          <el-button type="primary" class="btn-width"
            :style="{ background: themeObj.color, color: '#fff', borderColor: themeObj.color, width: '120px' }"
            @click="saveImage">保存图片</el-button>
        </div>
      </el-form>
    </DtPopup>

    <!-- 询价资料参考 -->
    <DtPopup :isShow.sync="showTipsPopup" @close="closeTips" title="热销产品参考" :footer="false">
      <div style="background-color: #e8efff;height: 60px;padding: 10px;line-height: 20px;display: flex">
        <div><i class="el-icon-info" style="color: #5c5ced"></i></div>
        <span style="margin-left: 7px;">该热销产品仅做参考，若未找到相似的产品，请返回输入框通过关键字检索，或联系您的平安对接人</span>
      </div>
<!--      <el-row>-->
<!--        <el-col :span="6">-->
<!--          <el-menu default-active="2" class="el-menu-vertical-demo">-->
<!--            <el-menu-item v-for="(item, index) in navBarlist" :key="index" @click="showProductDetail(item)">-->
<!--              <span slot="title">{{ item.name }}</span>-->
<!--            </el-menu-item>-->
<!--          </el-menu>-->
<!--        </el-col>-->
<!--        <el-col :span="18">-->
<!--          <el-table :data="tableDataList" class="dt-table" style="width: 100%;" v-hover>-->
<!--            <el-table-column align="center" prop="name" label="产品名称"></el-table-column>-->
<!--          </el-table>-->
<!--        </el-col>-->
<!--      </el-row>-->
      <div style="display: flex">
        <div class="nav-list1">
          <div
            v-for="(item, index) in navBarlist"
            :key="index"
            :class="{ li: true, active: currentIndex == index }"
            :style="{
          color: currentIndex == index ? themeObj.color : '',
          'border-color': currentIndex == index ? themeObj.color : '',
        }"
            @click="navChange(item,index)"
          >
            {{ item.name }}
          </div>
        </div>
        <div style="width: 80%;margin-left: 12px;">
            <el-table :data="tableDataList" class="dt-table" :header-cell-style="{'text-align':'left','font-style':'normal','background-color':'#fff'}" :cell-style="{'text-align':'left'}" style="width: 100%;" v-hover stripe>
              <el-table-column align="center" prop="name" label="产品名称"></el-table-column>
            </el-table>
        </div>
      </div>

      <div style="padding:20px 0;text-align: center;">
        <el-button type="primary" class="btn-width"
          :style="{ background: '#fff', color: themeObj.color, borderColor: themeObj.color, marginRight: '20px', width: '120px' }"
          @click="closeTips">关 闭</el-button>
      </div>
    </DtPopup>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import * as api from "@/api/userManagement/index.js";
import { validate, validateAlls } from "@/config/validation";
import { getDicItemList } from "@/config/tool.js";
import { getRolePage } from "@/api/roleManagement/index.js";
import { findLegalOrgDataByTenantId, findRolesByUserId, getRoleList } from "@/api/userManagement/index.js";
import { rootPath } from "@/utils/globalParam";
import html2canvas from "html2canvas";

export default {
  name: "inquiryAdd",
  data() {
    return {
      toolListProps: {
        toolTitle: "询价详情",
        toolList: []
      },
      entranceTitle: "线上询价记录",
      addData: {
        totalPremium: '0.00',
        policyholder:"",
        policyholderCreditCode:"",
        policyholderIndustryOne:"",
        policyholderIndustryTwo:"",
        insured:"",
        insuredCreditCode:"",
        insuredIndustryOne:"",
        insuredIndustryTwo:"",
        salesman:"",
        dealTime:"",
        productData: []
      },
      inquiryCompanyList: [],
      fileUploadUrl: `${rootPath}/api/upload/file`,
      detailFileParam: {
        fileType: "inquiryFile",
      },

      productPlanList: [],
      buttonClickStatus: false,
      buttonType: "primary",
      inquiryDetail: {
        opportunity: {
          opportunityId: "",
          opportunityName: ""
        },// 机会信息
        project: {
          projectId: "",
          projectName: "",
          submitTime: ""
        },// 项目信息
        customer: {
          policyholder: "",
          insured: "",
          policyholderCreditCode: "",
          insuredCreditCode: "",
          policyholderIndustryType: "",
          insuredIndustryType: ""
        },// 客户信息
        inquiryInfo: {
          salesman: "",
          dealTime: "",
          productData: [],
          notes: "",
          productPlanList: [],
          totalPremium: 0.00
        },// 询价信息
        inquiryOrder: []// 关联询价单
      },
      productCategoryList: [],
      productNameList: [],
      insuranceMaterials: [],// 投保材料

      addRules: {
        policyholder: [{ required: true, validator: validate, trigger: "blur" }],
        policyholderCreditCode: [{ required: true, validator: validate, trigger: "blur" }],
        policyholderIndustryOne: [{ required: true, validator: validate, trigger: "blur" }],
        policyholderIndustryTwo: [{ required: true, validator: validate, trigger: "blur" }],
        insured: [{ required: true, validator: validate, trigger: "blur" }],
        insuredCreditCode: [{ required: true, validator: validate, trigger: "blur" }],
        insuredIndustryOne: [{ required: true, validator: validate, trigger: "blur" }],
        insuredIndustryTwo: [{ required: true, validator: validate, trigger: "blur" }],
        salesman: [{ required: true, validator: validate, trigger: "blur" }],
        dealTime: [{ required: true, validator: validate, trigger: "blur" }],
      },
      productColor: 'gray',// 删除产品按钮颜色

      showPopup: false,// 询价参考资料
      popupData: {// 弹窗数据
        productCategoryCode: "",
        productCode: "",
        productName: ""
      },
      inquiryPopupList: [],// 询价参考资料弹窗 - 询价数据合集
      inquiryInsureList: [],// 询价参考资料弹窗 - 转投保数据合集
      showTipsPopup: false,
      currentIndex: 0,
      navBarlist: [],
      tableDataList: []
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    },
    currentLoginUser() {
      return this.$store.getters["layoutStore/getCurrentLoginUser"];
    },
    removeProductColor() {
      return this.productColor;
    },
  },
  filters: {
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    getEntranceTitle() {
      return this.entranceTitle;
    },
    goBack() {
      let entrance = this.$route.query.entrance;
      if (entrance == '2') {
        this.$router.push({
          name: "inquiryRecordList"
        });
      } else {
        this.$router.push({
          name: "userList"
        });
      }

    },
    async getDicFun() {
      this.inquiryCompanyList = await getDicItemList("elms.inquiry.company");
      this.productCategoryList = [
        { "value": "产品大类1-value", "label": "产品大类1" },
        { "value": "产品大类2-value", "label": "产品大类2" },
        { "value": "产品大类3-value", "label": "产品大类3" },
        { "value": "产品大类4-value", "label": "产品大类4" },
        { "value": "产品大类5-value", "label": "产品大类5" },
      ];

    },
    async initData() {

      this.entrance = this.$route.query.entrance;
      console.log(this.$route.query.opportunityId, '--------------queryId-------------------');

      if (this.$route.query.entrance == '2') {
        this.entranceTitle = "线上询价记录";
        this.buttonClickStatus = true;
        this.buttonType = 'info';
      } else {
        this.entranceTitle = "询价记录";
        this.buttonClickStatus = false;
        this.buttonType = 'primary';
      }

      this.addData.productData = [
        { index: 1, productCategoryCode: "", productCode: "", premium: "" }
      ];

      this.inquiryDetail.opportunity = {
        opportunityId: "25",
        opportunityName: "海底捞-员福-2025",
        inquiryCompanyName: "平安产险",
        agentName: "徐晨旭",
        agentCode: "6400000001",
        companyName: "北京分公司",
        projectManager: "李增额",
      };

      this.inquiryDetail.project = {
        projectId: "39239239923",
        projectName: "投保企业名称+产品大类 项目",
        submitTime: "2025-04-11 15:00:23"
      };

      this.inquiryDetail.customer = {
        policyholder: "四川海底捞餐饮股份有限公司",
        insured: "四川海底捞餐饮股份有限公司",
        policyholderCreditCode: "239239239923",
        insuredCreditCode: "239239239923",
        policyholderIndustryType: "餐饮业-正餐服务",
        insuredIndustryType: "餐饮业-正餐服务"
      };

      this.inquiryDetail.inquiryInfo = {
        salesman: "业务员姓名",
        dealTime: "2025-04-11",
        productData: [
          { index: 1, productCategoryCode: "产品大类1-value", productCode: "产品1-产品大类1-value", premium: "5000" }
        ],
        notes: "目前，[公司 / 单位] 在 [财产安全 / 员工保障 / 业务运营等方面] 存在一定风险隐患。例如，[具体说明风险情况，如公司固定资产面临自然灾害或意外事故损失风险、员工在工作中可能遭遇意外伤害、业务开展过程中可能因责任问题引发纠纷等]。为有效转移和规避这些风险，降低因意外事件造成的经济损失，保障公司正常运营和员工合法权益，采购相应的保险产品十分必要。",
        productPlanList: [
          {
            'fileUrl': 'https://www-sta.kbao123.com/gateway/oss2/kbc-ufs-sta/T0001/B00008/T0001/logo/2025/01/23/4e62d8/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20250123110249.png?Expires=2368321430&OSSAccessKeyId=LTAI4GJaRdNjjXCwyh28M7Vr&Signature=rY04uEOiG3DUGKQc%2FSrlGp9xABk%3D',
            'fileName': '文件文件名称1'
          },
          {
            'fileUrl': 'https://www-sta.kbao123.com/gateway/oss2/kbc-ufs-sta/T0001/B00008/T0001/logo/2024/01/26/ec9641/%E5%BE%AE%E4%BF%A1%E5%9B%BE%E7%89%87_20240126161813.png?Expires=2336977111&OSSAccessKeyId=LTAI4GJaRdNjjXCwyh28M7Vr&Signature=JzUNl%2FJpL%2Bk0Hr%2BelrCiZFohNdg%3D',
            'fileName': '文件文件名称2'
          }
        ],
        totalPremium: 5000
      };
      this.inquiryDetail.inquiryOrder = [
        {
          "inquiryNo": "Q056000390003825134446-1", "documentNo": "", "productCategory": "团意健", "productName": "平安团体意健自定义产品", "premium": "10000", "status": "2", "inquiryTime": "2025-05-12 02:01:24", "insurancePeriod": "2025-04-12 - 2026-04-11"
        },
        {
          "inquiryNo": "Q056000390003825134446-2", "documentNo": "", "productCategory": "团意健", "productName": "平安团体意健自定义产品", "premium": "10000", "status": "2", "inquiryTime": "2025-05-12 02:01:24", "insurancePeriod": "2025-04-12 - 2026-04-11"
        }
      ];

      this.productPlanList = [
        { fileUrl: "", fileName: "上传文件1" },
        { fileUrl: "", fileName: "上传文件2" },
      ];

      this.insuranceMaterials = [
        { productName: "xxxx", materials: "投保确认资料，客户证件" },
        { productName: "yyyy", materials: "投保确认资料，客户证件" },
      ];
      // this.initParam.param.tenantId = this.tenantId;
      // let res = await api.getUserList(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    // 机会详情
    goOpportunityDetail() {
      this.$router.push({
        name: "opportunityDetails",
        query: {
          id: this.inquiryDetail.opportunity.opportunityId
        }
      });
    },
    // 产品大类级联查询产品方法
    changeCategory(row) {
      console.log(row.productCategoryCode, '-------val------');
      this.addData.productData[row.index - 1].productCode = '';
      this.addData.productData[row.index - 1].premium = '';
      let val = row.productCategoryCode;
      this.productNameList = [
        { "value": "产品1-" + val, "label": "产品1" + val },
        { "value": "产品2-" + val, "label": "产品2" + val },
        { "value": "产品3-" + val, "label": "产品3" + val },
        { "value": "产品4-" + val, "label": "产品4" + val },
        { "value": "产品5-" + val, "label": "产品5" + val }
      ];

    },

    // 选择产品方法
    changeProduct(row) {

    },

    showTips() {

      this.navBarlist = [
        {
          "name": "企财险", "products": [
            { "name": "财产一切险" },
            { "name": "财产基本险" },
            { "name": "财产综合险" },
            { "name": "财产一切险营业中断险" },
          ]
        },
        {
          "name": "工程险", "products": [
            { "name": "建筑工程一切险及第三者责任险(CAR)" },
            { "name": "安装工程一切险及第三者责任险(EAR)" },
            { "name": "平安装修保2022" },
          ]
        },
        {
          "name": "高端责任险", "products": [
            { "name": "董监事及高级管理人员责任保险-A股" },
          ]
        },
        { "name": "责任险", "products": [] },
      ];

      this.tableDataList = this.navBarlist[0].products;
      this.currentIndex = 0;

      this.showTipsPopup = true;
    },
    showProductDetail(item) {
      console.log(item, '-----')
      this.tableDataList = item.products;
    },

    closeTips() {
      this.showTipsPopup = false;
    },
    navChange(item,index) {
      this.tableDataList = item.products;
      this.currentIndex = index;

    },
    // 添加产品
    addProduct() {
      let index = this.addData.productData.length;
      this.productColor = "red";
      this.addData.productData.push({ index: index + 1, productCategoryCode: "", productCode: "", premium: "" });
    },

    // 删除产品
    removeProduct(row) {
      if (this.addData.productData.length == 1) {
        return;
      }
      const index = this.addData.productData.findIndex(item => item.index === row.index)
      if (index > -1) {
        this.addData.productData.splice(index, 1)
      }
      if (this.addData.productData.length == 1) {
        this.productColor = "gray";
      }
      // 删除后重新计算总保费
      this.countPremium();
    },

    // 计算总保费
    countPremium() {
      if (this.addData.productData && this.addData.productData.length > 0) {
        let totalPremium = 0;
        this.addData.productData.forEach(item => {
          if (item.premium) {
            totalPremium += Number(item.premium);
          }
        });
        totalPremium = (totalPremium / 10000).toFixed(2);
        this.addData.totalPremium = totalPremium;
      }
    },

    // 上传相关方法
    handleExceed() {
      this.$message.warning(`最多上传 10 个文件`);
    },
    inquiryBeforeUpload(file) {
      if (!(
        this._.endsWith(file.name, "pdf") ||
        this._.endsWith(file.name, "ppt") ||
        this._.endsWith(file.name, "pptx") ||
        this._.endsWith(file.name, "xls") ||
        this._.endsWith(file.name, "doc") ||
        this._.endsWith(file.name, "docx") ||
        this._.endsWith(file.name, "png") ||
        this._.endsWith(file.name, "jpg") ||
        this._.endsWith(file.name, "zip") ||
        this._.endsWith(file.name, "rar") ||
        this._.endsWith(file.name, "7z"))
      ) {
        this.$message.error("文件格式不正确");
        return false;
      }

      let size = file.size / 1024 / 1024;
      if (size > 20) {
        this.$message.error("单个文件不能超过20M");
        return;
      }
      this.detailFileParam.fileType = "inquiry";
      return true;
    },

    // 上传成功
    uploadSuccess(response, file) {
      console.log(response, '---------response------------');
      console.log(file, '-----------file-----------');
      this.productPlanList.push({ fileUrl: file.name, fileName: file.name });
      // this.productPlanUrlList.push(response.datas);
    },


    copyPolicyholderInfo() {
      this.addData.insured = this.addData.policyholder;
      this.addData.insuredCreditCode = this.addData.policyholderCreditCode;
      this.addData.insuredIndustryOne = this.addData.policyholderIndustryOne;
      this.addData.insuredIndustryTwo = this.addData.policyholderIndustryTwo;
    },

    // 删除文件
    deleteProductPlanFile(item, index) {
      console.log(index, '----index-----')
      this.productPlanList.splice(index, 1);
    },
    // 暂存
    temporarySave() { },
    // 提交
    submitInquiry() {
      console.log('0000000000000000')
      if (!validateAlls(this.$refs.addForm)){return;}
    },

    // 打开询价材料弹窗
    showMaterialPopup() {
      this.showPopup = true;
    },
    // 询价参考弹窗
    changeReferenceCategory(val) {
      console.log(val, '-------item------');
      this.popupData.productCode = "";
      this.popupData.productName = "";
      this.inquiryPopupList = [];
      this.inquiryInsureList = [];
      this.productNameList = [
        { "value": "产品1-" + val, "label": "产品1" + val },
        { "value": "产品2-" + val, "label": "产品2" + val },
        { "value": "产品3-" + val, "label": "产品3" + val },
        { "value": "产品4-" + val, "label": "产品4" + val },
        { "value": "产品5-" + val, "label": "产品5" + val }
      ];
    },

    changeReferenceProduct(val) {
      this.popupData.productName = val;


      this.inquiryPopupList = [
        { name: "工程险资料", value: "工程险资料" }
      ];

      this.inquiryInsureList = [
        { name: "客户证件", value: "客户证件" },
        { name: "投保确认资料", value: "投保确认资料" }
      ];

    },


    // 关闭弹窗
    closePopup() {
      this.showPopup = false;
    },
    // 保存图片到本地
    saveImage() {
      html2canvas(this.$refs.productImage, {
        useCORS: true,
        scale: 1
      }).then((canvas) => {
        let url = canvas.toDataURL("image/png");
        let a = document.createElement("a");
        let envent = new MouseEvent("click");
        a.download = "询价资料参考";
        a.href = url;
        a.dispatchEvent(envent);
      });
    },

  }
};
</script>
<style lang="less">
.inquiry-add {
  .log-tool {
    margin-top: 20px;
  }

  .link-text {
    color: #409EFF;
    cursor: pointer;
    text-decoration: underline;
    font-size: 14px;
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 20px;
    text-align: center;
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    flex-wrap: wrap;
    padding-left: 10px;

    .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before, .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
      //display: none;
    }

    .item {
      font-size: 14px;
      color: #333;
      width: 45%;
      padding: 0 10px;
      display: flex;
      align-items: center;
      margin-bottom: 30px;

      .item-rule {
        width: 180px;
        text-align: right;
        margin-right: 20px;
      }

      .item-rule span {
        color: red;
        margin-right: 10px;
      }

      .item-rule-span {
        color: red;
        margin-right: 10px;
      }
    }
  }

  .info-item-order {
    padding-left: 20px;
    width: 80%;
  }

  .tool-wrap {
    padding: 20px 20px 0 20px;
  }

  .item-tips {
    font-size: 14px;
    color: #333;
    padding-left: 20px;
  }

  .item-file {
    margin-left:70px;

    .product-item {
      display: flex;
      background-color: rgba(242, 242, 242, 1);
      border-radius: 5px;
      margin-top: 15px;
      height: 50px;
      //padding-top: 12px;
      width: 50%;
    }
  }

  .foot-btn {
    text-align: center;
    padding: 30px;
  }

  .item-premium {
    display: flex;
    flex-wrap: wrap;
    border: 1px solid #EBEEF5;
    width: 85%;

    .item-button {
      text-align: left;
      width: 50%;
      height: 60px;
      padding-top: 12px;
      padding-left: 20px;
    }

    .item-total-premium {
      text-align: right;
      width: 50%;
      height: 60px;
      padding: 20px 100px;
    }

  }

  .product-plan-config {
    .materials-item {
      margin-top: 10px;

      .item {
        width: 60%;
        padding-left: 10px;
        color: #FF6600;
      }

    }
  }


  .inquiry-popup {
    height: 40px;

    .item {
      background: #e8efff;
      height: 40px;
      padding: 11px;
      font-weight: 600;
    }

  }

  .inquiry-popup-category {
    height: 40px;

    .item {
      width: 12%;
      height: 40px;
      padding-top: 10px;
    }

    .item-select {
      width: 36%;
      height: 40px;
    }
  }


  .nav-list1 {
    //overflow: hidden;
    //width: 100%;
    //display:grid;
    display: flex;
    flex-direction: column; /* 子元素沿水平方向排列 */
    align-items: center;

    .li {
      width: 108px;
      height: 42px;
      background: #f9f9f9;
      text-align: center;
      line-height: 40px;
      color: #999;
      font-size: 14px;
      float: left;
      //margin-left: 14px;
      cursor: pointer;

      &.active {
        color: #4f85e6;
        height: 42px;
        background: #fff;
      }
    }
  }

}
</style>
