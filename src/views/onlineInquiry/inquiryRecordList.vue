<template>
  <div class="inquiry-list">

    <TableToolTemp :toolListProps="toolListProps" @handleTool="handleTool" class="log-tool"></TableToolTemp>

    <SearchForm :searchForm="initParam" :searchFormTemp="searchFormTemp" @normalSearch="normalSearch"
                @normalResetQuery="normalResetQuery"></SearchForm>

    <el-table :data="tableData" class="dt-table" style="width: 100%" v-hover>

      <el-table-column type="expand">
        <template slot-scope="props">
          <el-table :data="props.row.priceList">
            <el-table-column align="center" prop="inquiryNo" label="询价单号"></el-table-column>
            <el-table-column align="center" prop="documentNo" label="单证号"></el-table-column>
            <el-table-column align="center" prop="productCategory" label="产品大类"></el-table-column>
            <el-table-column align="center" prop="productName" label="产品名称"></el-table-column>
            <el-table-column align="center" prop="premium" label="保费"></el-table-column>
            <el-table-column align="center" prop="status" label="状态"></el-table-column>
            <el-table-column align="center" prop="inquiryTime" label="询价时间"></el-table-column>
            <el-table-column align="center" prop="insurancePeriod" label="保险期限"></el-table-column>
            <el-table-column align="center" label="操作">
              <template slot-scope="scope">
                <el-button class="btn-center" type="text" @click="download(scope.row)">下载报价单</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>

      <el-table-column align="center" prop="opportunityId" label="机会ID"></el-table-column>
      <el-table-column align="center" prop="opportunityName" label="机会名称"></el-table-column>
      <el-table-column align="center" label="服务顾问">
        <template slot-scope="scope">
          {{ scope.row.agentName }}({{ scope.row.agentCode }})
        </template>
      </el-table-column>
      <el-table-column align="center" prop="companyName" label="所属机构"></el-table-column>
      <el-table-column align="center" prop="projectManagerName" label="项目经理"></el-table-column>
      <el-table-column align="center" prop="inquiryCompany" label="询价公司"></el-table-column>
      <el-table-column align="center" prop="projectId" label="项目编号"></el-table-column>
      <el-table-column align="center" prop="projectName" label="项目名称"></el-table-column>
      <el-table-column align="center" prop="policyholder" label="投保人"></el-table-column>
      <el-table-column align="center" prop="insured" label="被保人"></el-table-column>
      <el-table-column align="center" prop="productName" label="产品名称"></el-table-column>
      <el-table-column align="center" prop="dealTime" label="预计成交时间"></el-table-column>
      <el-table-column align="center" label="预计总保费">
        <template slot-scope="scope">
          {{ scope.row.premium }} 元
        </template>
      </el-table-column>
      <el-table-column align="center" prop="salesman" label="平安业务员"></el-table-column>
      <el-table-column align="center" prop="creator" label="创建人"></el-table-column>
      <el-table-column align="center" prop="submitTime" label="提交时间"></el-table-column>
      <el-table-column align="center" label="状态">
        <template slot-scope="scope">
          {{ scope.row.status | getDicItemName("elms.inquiry.status") }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="170px">
        <template slot-scope="scope">
          <el-button class="btn-center" type="text" @click="view(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :pageData="initParam"
                :total="total" layout="total, sizes, prev, pager, next, jumper"></Pagination>

  </div>
</template>
<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import { getDicItemList } from "@/config/tool.js";
import {
  findLegalOrgData,
} from "@/api/userManagement/index.js";

export default {
  name: "inquiryRecordList",
  data() {
    return {
      toolListProps: {
        toolTitle: "线上询价记录",
        toolList: []
      },
      tableData: [],
      initParam: {
        pageNum: 1,
        pageSize: 10,
        param: {
          agentName: "",
          legalCode: "",
          opportunityName: "",
          opportunityId: "",
          projectManagerName: "",
          inquiryCompany: "",
          projectName: "",
          salesman: "",
          status: "",
          submitTimeStart: "",
          submitTimeEnd: ""
        }
      },
      searchFormTemp: [
        {
          label: "顾问姓名/用户名",
          name: "agentName",
          type: "input",
          width: "200px"
        },
        {
          label: "所属机构",
          name: "legalCode",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "机会ID",
          name: "opportunityId",
          type: "input",
          width: "200px",
        },
        {
          label: "机会名称",
          name: "opportunityName",
          type: "input",
          width: "200px"
        },
        {
          label: "项目经理",
          name: "projectManagerName",
          type: "input",
          width: "200px",
        },
        {
          label: "询价公司",
          name: "inquiryCompany",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "项目名称",
          name: "projectName",
          type: "input",
          width: "200px"
        },
        {
          label: "平安业务员",
          name: "salesman",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          label: "状态",
          name: "status",
          type: "select",
          width: "200px",
          list:[]
        },
        {
          name: "createTime",
          type: "doubleDate",
          label: "机会提交时间",
          placeholder: "请选择",
          elType: "DateTimePicker",
          options: [
            {
              name: "submitTimeStart",
              placeholder: "请输入开始时间",
              value: "",
            },
            {
              name: "submitTimeEnd",
              placeholder: "请输入结束时间",
              value: "",
            },
          ],
          fixedShow: true,
        }
      ],
      total: 0,
      legalList:[],
    };
  },
  components: {
    TableToolTemp,
    SearchForm,
  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    tenantId() {
      return this.$store.state.layoutStore.currentLoginUser.tenantId;
    }
  },
  filters: {
    getRoleType(list, roleType) {
      let index = list.findIndex((v) => v.dicItemCode == roleType);
      return index > -1 ? list[index].dicItemName : "-";
    }
  },
  async created() {
    await this.getDicFun();
    this.initData();
  },
  methods: {
    async getDicFun() {

      let legalListTemp = await findLegalOrgData({});

      legalListTemp.forEach(item => {
        this.legalList.push({
          dicItemName: item.orgName,
          dicItemCode: item.orgCode
        });
      });
      // 所属机构
      this.searchFormTemp[1].list = this.legalList;

      // 状态
      // await getDicItemList("sys.gateway.status");
      // this.relationTypeList = await getDicItemList("elms.user.belong.type");
      // 询价公司
      this.searchFormTemp[5].list = await getDicItemList("elms.inquiry.company");
      this.searchFormTemp[7].list = await getDicItemList("elms.inquiry.status");
      this.searchFormTemp[8].list = await getDicItemList("elms.inquiry.status");

    },
    async initData() {
      this.initParam.param.tenantId = this.tenantId;
      this.tableData = [
        {"opportunityId":"1","opportunityName":"机会名称1","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"1",
          "priceList":[
            {
              "inquiryNo":"Q056000390003825134446","documentNo":"","productCategory":"团意健","productName":"平安团体意健自定义产品","premium":"10000","status":"2","inquiryTime":"2025-05-12 02:01:24","insurancePeriod":"2025-04-12 - 2026-04-11"
            }
          ]},
        {"opportunityId":"2","opportunityName":"机会名称2","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"1",
          "priceList":[
            {
              "inquiryNo":"Q056000390003825134446-1","documentNo":"","productCategory":"团意健","productName":"平安团体意健自定义产品","premium":"10000","status":"2","inquiryTime":"2025-05-12 02:01:24","insurancePeriod":"2025-04-12 - 2026-04-11"
            },
            {
              "inquiryNo":"Q056000390003825134446-2","documentNo":"","productCategory":"团意健","productName":"平安团体意健自定义产品","premium":"10000","status":"2","inquiryTime":"2025-05-12 02:01:24","insurancePeriod":"2025-04-12 - 2026-04-11"
            }
          ]},
        {"opportunityId":"3","opportunityName":"机会名称3","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"2"},
        {"opportunityId":"4","opportunityName":"机会名称4","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"3"},
        {"opportunityId":"5","opportunityName":"机会名称5","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"4"},
        {"opportunityId":"6","opportunityName":"机会名称6","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"1"},
        {"opportunityId":"7","opportunityName":"机会名称7","agentCode":"zhangsan","agentName":"张三","companyName":"xxxx机构","projectManagerName":"项目经理","inquiryCompany":"询价公司","projectId":"产品ID","projectName":"产品名称","policyholder":"投保人","insured":"被保人","productName":"产品列表首条产品名称","dealTime":"2025-09-09","premium":"5000","salesman":"业务员姓名","creator":"提交询价单人员","submitTime":"2025-04-11 15:00:23","status":"5"}
      ];

      this.total = this.tableData.length;

      // let res = await api.getUserList(this.initParam);
      // if (res) {
      //   this.total = res.total;
      //   this.tableData = [];
      //   if (res.list) {
      //     this.tableData = res.list ? res.list : [{}];
      //   }
      // }
    },
    async handleTool(item) {
      if (item.name == "新增人员") {
        this.isAdd = true;
        this.popupTitle = "新增人员";
        this.selectIndustryTypes = [];
        this.selectInsuranceTypes = [];
        this.addData = _.cloneDeep(this.$options.data().addData);
        this.showPopup = true;
      }
    },

    // 搜索
    normalSearch(data) {
      this.initParam = data;
      this.initData();
    },
    // 重置
    normalResetQuery() {
      this.initParam = _.cloneDeep(this.$options.data().initParam);
      this.initData();
    },
    async view(row) {
      this.$router.push({
        name:"inquiryDetail",
        query:{
          opportunityId:row.opportunityId,
          entrance:"2"
        }
      })
    },
    // 下载报价单
    download(row){

    },

    handleSizeChange(val) {
      this.initParam.pageSize = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.initParam.pageNum = val;
      this.initData();
    },
  }
};
</script>
<style lang="less">
.inquiry-list {
  .nav-list {
    overflow: hidden;
    background-color: #f0f2f5;
    width: 100%;

    .li {
      width: 108px;
      height: 46px;
      background: #ececec;
      border-radius: 6px 6px 0px 0px;
      text-align: center;
      line-height: 46px;
      color: #999;
      font-size: 16px;
      float: left;
      margin-left: 14px;
      cursor: pointer;

      &.active {
        font-size: 18px;
        color: #4f85e6;
        background: #fff;
        border-top: 2px solid #4f85e6;
      }
    }

  }
}

</style>
