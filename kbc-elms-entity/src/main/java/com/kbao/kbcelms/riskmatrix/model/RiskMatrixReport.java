package com.kbao.kbcelms.riskmatrix.model;

import com.kbao.kbcelms.riskmatrix.bean.RiskMatrixResultDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 风险矩阵报告MongoDB实体类
 * 用于存储风险矩阵计算报告的完整数据
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "risk_matrix_report")
public class RiskMatrixReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    private String id;

    /**
     * 企业ID
     */
    @Indexed
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业类型（A类、B类、C类、D类、E类、CDE等）
     */
    @Indexed
    private String enterpriseType;

    /**
     * 报告状态（DRAFT-草稿，COMPLETED-完成，ARCHIVED-归档）
     */
    @Indexed
    private String reportStatus;

    /**
     * 报告版本号
     */
    private String reportVersion;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 平均分
     */
    private BigDecimal averageScore;

    /**
     * 整体风险等级（低风险、中风险、高风险、极高风险）
     */
    private String overallRiskLevel;

    /**
     * 类别结果列表
     */
    private List<CategoryResult> categoryResults;

    /**
     * 计算过程详情
     */
    private CalculationProcess calculationProcess;

    /**
     * 雷达图数据
     */
    private List<RiskMatrixResultDTO> radarChartDatas;

    /**
     * 建议措施
     */
//    private List<RiskSuggestion> suggestions;

    /**
     * 计算开始时间
     */
    private Date calculationStartTime;

    /**
     * 计算结束时间
     */
    private Date calculationEndTime;

    /**
     * 计算耗时（毫秒）
     */
    private Long calculationDuration;

    /**
     * 创建时间
     */
    @Indexed
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private String updaterId;

    /**
     * 更新人姓名
     */
    private String updaterName;

    /**
     * 租户ID
     */
    @Indexed
    private String tenantId;

    /**
     * 类别结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryResult implements Serializable {

        /**
         * 风险矩阵ID
         */
        private Long riskMatrixId;
        /**
         * 风险矩阵名称
         */
        private String riskMatrixName;

        /**
         * 类别ID
         */
        private Long categoryId;

        /**
         * 类别名称
         */
        private String categoryName;

        /**
         * 类别描述
         */
        private String categoryDescription;

        /**
         * 计算方式（sum-加和，average-平均数）
         */
        private String calculationMethod;

        /**
         * 类别得分
         */
        private BigDecimal categoryScore;

        /**
         * 权重
         */
        private BigDecimal weight;

        /**
         * 加权得分
         */
        private BigDecimal weightedScore;

        /**
         * 档次信息
         */
        private LevelInfo level;

        /**
         * 评分项结果列表
         */
        private List<ScoreItemResult> scoreItems;

        /**
         * 排序号
         */
        private Integer sort;
    }

    /**
     * 档次信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelInfo implements Serializable {

        /**
         * 档次ID
         */
        private Long levelId;

        /**
         * 档次名称（一档、二档、三档、四档、五档）
         */
        private String name;

        /**
         * 档次描述
         */
        private String description;

        /**
         * 最小值
         */
        private BigDecimal minValue;

        /**
         * 最大值
         */
        private BigDecimal maxValue;

        /**
         * 分数范围显示（如：80-100）
         */
        private String scoreRange;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 风险等级颜色
         */
        private String riskLevelColor;
    }

    /**
     * 评分项结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreItemResult implements Serializable {

        /**
         * 评分项ID
         */
        private Long scoreItemId;

        /**
         * 评分项名称
         */
        private String scoreItemName;

        /**
         * 关联公式名称
         */
        private String formulaName;

        /**
         * 公式
         */
        private String formula;

        /**
         * 问卷得分
         */
        private BigDecimal questionnaireScore;

        /**
         * 公式计算得分
         */
        private BigDecimal calculatedScore;

        /**
         * 系数
         */
        private BigDecimal coefficient;

        /**
         * 权重
         */
        private BigDecimal weight;

        /**
         * 最终得分
         */
        private BigDecimal finalScore;

        /**
         * 计算过程说明
         */
        private String calculationDescription;
    }

    /**
     * 计算过程详情内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalculationProcess implements Serializable {

        /**
         * 问卷答案列表
         */
        private List<QuestionnaireAnswerDetail> questionnaireAnswers;

        /**
         * 评分项计算结果
         */
        private List<ScoreItemResult> scoreItemResults;

        /**
         * 基础参数
         */
        private BasicParameters basicParameters;

        /**
         * 风险矩阵配置信息
         */
        private RiskMatrixInfo riskMatrix;

        /**
         * 计算步骤说明
         */
        private List<String> calculationSteps;
    }
    /**
     * 问卷答案详情内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuestionnaireAnswerDetail implements Serializable {

        /**
         * 问题ID
         */
        private Long questionId;

        /**
         * 题目
         */
        private String questionTitle;
        /**
         * 评分项ID
         */
        private Long scoreId;

        /**
         * 答案内容（选择题存选项值，简答题存文本）
         */
        private String answerContent;
        /**
         * 选项值
         */
        private String optionValue;
        /**
         * 选项分值
         */
        private Integer optionScore;

        /**
         * 得分
         */
        private BigDecimal score;
    }

    /**
     * 基础参数内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BasicParameters implements Serializable {

        /**
         * 企业类型
         */
        private String enterpriseType;

        /**
         * 计算时间
         */
        private Date calculationTime;

        /**
         * 使用的风险矩阵配置
         */
        private String matrixConfigVersion;

        /**
         * 其他参数（JSON格式）
         */
        private String otherParams;
    }

    /**
     * 风险矩阵信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskMatrixInfo implements Serializable {

        /**
         * 风险矩阵ID
         */
        private Long matrixId;

        /**
         * 风险矩阵名称
         */
        private String matrixName;

        /**
         * 风险矩阵描述
         */
        private String matrixDescription;

        /**
         * 适用企业类型
         */
        private List<String> enterpriseTypes;

        /**
         * 排序号
         */
        private Integer sort;
    }

    /**
     * 风险建议内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskSuggestion implements Serializable {

        /**
         * 建议类型（OVERALL-整体建议，CATEGORY-类别建议）
         */
        private String suggestionType;

        /**
         * 关联的类别ID（类别建议时有值）
         */
        private Long categoryId;

        /**
         * 关联的类别名称（类别建议时有值）
         */
        private String categoryName;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 建议标题
         */
        private String title;

        /**
         * 建议内容
         */
        private String content;

        /**
         * 优先级（HIGH-高，MEDIUM-中，LOW-低）
         */
        private String priority;

        /**
         * 排序号
         */
        private Integer sort;
    }
}
