package com.kbao.kbcelms.riskmatrix.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险矩阵详情视图对象（包含完整的嵌套结构）
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskMatrixDetailVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 矩阵名称
     */
    private String name;
    
    /**
     * 矩阵编码
     */
    private String code;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 矩阵分类
     */
    private String category;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 关联企业类型，逗号分隔
     */
    private String enterpriseTypes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 核心类别数量
     */
    private Integer categoryCount;
    
    /**
     * 风险矩阵类别列表
     */
    private List<CategoryDetailVO> categories;

    /**
     * 风险矩阵类别详情视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CategoryDetailVO {
        
        /**
         * 类别ID
         */
        private Long id;
        
        /**
         * 风险矩阵ID
         */
        private Long matrixId;
        
        /**
         * 类别名称
         */
        private String name;

        /**
         * 类别描述
         */
        private String description;

        /**
         * 权重
         */
        private BigDecimal weight;
        
        /**
         * 计算方法：sum-求和，avg-平均值，max-最大值
         */
        private String calculationMethod;

        /**
         * 排序序号
         */
        private Integer sortOrder;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;

        /**
         * 档次数量（查询时统计）
         */
        private Integer levelCount;
        
        /**
         * 关联的评分项列表
         */
        private List<ScoreItemDetailVO> scoreItems;
    }

    /**
     * 评分项详情视图对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreItemDetailVO {
        
        /**
         * 主键ID
         */
        private Long id;
        
        /**
         * 评分项名称
         */
        private String name;
        
        /**
         * 评分项编码
         */
        private String code;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 所属类别
         */
        private String category;
        
        /**
         * 权重
         */
        private BigDecimal weight;
        
        /**
         * 最大分值
         */
        private Integer maxScore;

        /**
         * 是否关联公式：0-否，1-是
         */
        private Integer isFormula;
        
        /**
         * 关联公式ID
         */
        private Long formulaId;
        
        /**
         * 公式名称
         */
        private String formulaName;
        
        /**
         * 系数
         */
        private BigDecimal coefficient;
        
        /**
         * 适用企业类型，逗号分隔
         */
        private String enterpriseTypes;
        
        /**
         * 状态：0-禁用，1-启用
         */
        private Integer status;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
        
        /**
         * 创建人
         */
        private String createUser;
        
        /**
         * 更新人
         */
        private String updateUser;
    }
}
