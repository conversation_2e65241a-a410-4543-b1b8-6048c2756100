package com.kbao.kbcelms.enterprise.query.record.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业创建信息记录MongoDB实体
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
@Document(collection = "enterprise_creation_record")
public class EnterpriseCreationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 企业统一信用代码
     */
    @Indexed
    @ApiModelProperty(value = "企业统一信用代码", example = "91110000123456789X")
    private String agentCode;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", example = "某保险经纪有限公司")
    private String enterpriseName;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名", example = "张三")
    private String creator;

    /**
     * 创建人工号
     */
    @Indexed
    @ApiModelProperty(value = "创建人工号", example = "AG001")
    private String creatorCode;

    /**
     * 创建时间
     */
    @Indexed
    @ApiModelProperty(value = "创建时间", example = "2025-08-22 10:30:00")
    private Date createTime;

    /**
     * 区域中心名称
     */
    @ApiModelProperty(value = "区域中心名称", example = "华北区域中心")
    private String areaCenterName;

    /**
     * 区域中心编码
     */
    @ApiModelProperty(value = "区域中心编码", example = "AC001")
    private String areaCenterCode;

    /**
     * 营业部名称
     */
    @ApiModelProperty(value = "营业部名称", example = "北京营业部")
    private String tradingCenterName;

    /**
     * 营业部编码
     */
    @ApiModelProperty(value = "营业部编码", example = "TC001")
    private String tradingCenterCode;

    /**
     * 所属团队名称
     */
    @ApiModelProperty(value = "所属团队名称", example = "第一业务团队")
    private String bpGroupName;

    /**
     * 所属团队编码
     */
    @ApiModelProperty(value = "所属团队编码", example = "BG001")
    private String bpGroupCode;

    /**
     * 企业类型：A-央企，B-上市公司，C-大型企业，D-中小企业
     */
    @ApiModelProperty(value = "企业类型", example = "C", notes = "A-央企，B-上市公司，C-大型企业，D-中小企业")
    private String dtType;

    /**
     * 企业规模
     */
    @ApiModelProperty(value = "企业规模", example = "大型")
    private String enterpriseScale;

    /**
     * 人员规模
     */
    @ApiModelProperty(value = "人员规模", example = "500-1000人")
    private String staffScale;

    /**
     * 所在城市
     */
    @ApiModelProperty(value = "所在城市", example = "北京市")
    private String city;

    /**
     * 行政区划代码
     */
    @ApiModelProperty(value = "行政区划代码", example = "110000")
    private String districtCode;

    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入", example = "1000-5000万元")
    private String annualIncome;

    /**
     * 行业分类代码
     */
    @ApiModelProperty(value = "行业分类代码", example = "J66")
    private String categoryCode;

    /**
     * 行业分类名称
     */
    @ApiModelProperty(value = "行业分类名称", example = "保险业")
    private String categoryName;

    /**
     * 企业联系人
     */
    @ApiModelProperty(value = "企业联系人", example = "张经理")
    private String enterpriseContacter;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话", example = "13800138000")
    private String contacterPhone;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息", example = "重点客户，需要重点关注")
    private String remark;

    /**
     * 是否验真：0-未验真，1-已验真
     */
    @ApiModelProperty(value = "是否验真", example = "1", notes = "0-未验真，1-已验真")
    private String isVerified;

    /**
     * 验真时间
     */
    @ApiModelProperty(value = "验真时间", example = "2025-08-22 10:30:00")
    private Date verifyTime;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称", example = "某某保险经纪有限公司")
    private String legalName;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码", example = "LEGAL001")
    private String legalCode;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "TENANT001")
    private String tenantId;

    /**
     * 机会总数
     */
    @ApiModelProperty(value = "机会总数", example = "5")
    private Integer opportunityNum;

    /**
     * 进行中机会类型
     */
    @ApiModelProperty(value = "进行中机会类型", example = "财产险,责任险")
    private String underwayInsureTypes;

    /**
     * 已锁定机会类型
     */
    @ApiModelProperty(value = "已锁定机会类型", example = "财产险")
    private String lockInsureTypes;

    /**
     * 已出单机会类型
     */
    @ApiModelProperty(value = "已出单机会类型", example = "责任险")
    private String issuedInsureTypes;
}
