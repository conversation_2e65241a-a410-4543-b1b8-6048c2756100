package com.kbao.kbcelms.enterprise.query.record.model;

import com.alibaba.fastjson.JSONObject;import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业创建信息记录MongoDB实体
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
@Document(collection = "enterprise_create_record")
public class EnterpriseCreateRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * MongoDB主键
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 代理人编码
     */
    @Indexed
    @ApiModelProperty(value = "代理人编码", example = "AG001")
    private String agentCode;

    /**
     * 代理人姓名
     */
    @ApiModelProperty(value = "代理人姓名", example = "张三")
    private String agentName;

    /**
     * 输入的企业名称
     */
    @ApiModelProperty(value = "输入的企业名称", example = "某保险经纪有限公司")
    private String inputEnterpriseName;

    /**
     * 是否有数据：0-无数据，1-有数据
     */
    @ApiModelProperty(value = "是否有数据", example = "1", notes = "0-无数据，1-有数据")
    private String hasData;

    /**
     * 是否验真：0-未验真，1-已验真
     */
    @ApiModelProperty(value = "是否验真", example = "1", notes = "0-未验真，1-已验真")
    private String isVerify;

    /**
     * 是否被阻止：0-未阻止，1-已阻止
     */
    @ApiModelProperty(value = "是否被阻止", example = "0", notes = "0-未阻止，1-已阻止")
    private String isBlocked;

    /**
     * 输入信息
     */
    @ApiModelProperty(value = "输入信息", example = "用户输入的查询信息")
    private JSONObject inputData;

    /**
     * 第三方返回信息
     */
    @ApiModelProperty(value = "第三方返回信息", example = "第三方接口返回的数据")
    private JSONObject thirdPartyData;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2025-08-22 10:30:00")
    private Date createTime;

    private String tenantId;
}
