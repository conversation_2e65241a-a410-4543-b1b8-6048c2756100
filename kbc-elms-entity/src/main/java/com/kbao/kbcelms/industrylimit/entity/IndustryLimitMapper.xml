<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kbao.kbcelms.industrylimit.dao.IndustryLimitMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.kbao.kbcelms.industrylimit.entity.IndustryLimit">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- VO结果映射 -->
    <resultMap id="VOResultMap" type="com.kbao.kbcelms.industrylimit.vo.IndustryLimitVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 详细VO结果映射（包含条件和动作） -->
    <resultMap id="DetailVOResultMap" type="com.kbao.kbcelms.industrylimit.vo.IndustryLimitVO">
        <id column="rule_id" property="id" jdbcType="BIGINT"/>
        <result column="rule_name" property="name" jdbcType="VARCHAR"/>
        <result column="rule_code" property="code" jdbcType="VARCHAR"/>
        <result column="rule_description" property="description" jdbcType="VARCHAR"/>
        <result column="rule_status" property="status" jdbcType="INTEGER"/>
        <result column="rule_create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="rule_update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="rule_create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="rule_update_user" property="updateUser" jdbcType="VARCHAR"/>
        <collection property="conditions" ofType="com.kbao.kbcelms.industrylimit.vo.IndustryLimitConditionVO">
            <id column="condition_id" property="id" jdbcType="BIGINT"/>
            <result column="condition_field" property="field" jdbcType="VARCHAR"/>
            <result column="condition_operator" property="operator" jdbcType="VARCHAR"/>
            <result column="condition_value" property="value" jdbcType="VARCHAR"/>
            <result column="condition_description" property="description" jdbcType="VARCHAR"/>
            <result column="condition_sort_order" property="sortOrder" jdbcType="INTEGER"/>
        </collection>
        <collection property="actionList" ofType="com.kbao.kbcelms.industrylimit.vo.IndustryLimitActionVO">
            <id column="action_id" property="id" jdbcType="BIGINT"/>
            <result column="action_type" property="type" jdbcType="VARCHAR"/>
            <result column="action_service_ids" property="serviceIdsJson" jdbcType="VARCHAR"/>
            <result column="action_description" property="description" jdbcType="VARCHAR"/>
            <result column="action_sort_order" property="sortOrder" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, code, description, status, create_time, update_time, 
        create_user, update_user, tenant_id, is_deleted
    </sql>

    <!-- 查询字段 -->
    <sql id="Select_Column_List">
        id, name, code, description, status, create_time, update_time, 
        create_user, update_user
    </sql>

    <!-- 查询条件 -->
    <sql id="Where_Clause">
        <where>
            is_deleted = 0
            <if test="param != null">
                <if test="param.name != null and param.name != ''">
                    AND name LIKE CONCAT('%', #{param.name}, '%')
                </if>
                <if test="param.code != null and param.code != ''">
                    AND code LIKE CONCAT('%', #{param.code}, '%')
                </if>
                <if test="param.status != null">
                    AND status = #{param.status}
                </if>
                <if test="param.createUser != null and param.createUser != ''">
                    AND create_user = #{param.createUser}
                </if>
            </if>
        </where>
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 查询行业限制规则列表 -->
    <select id="selectIndustryLimitList" resultMap="VOResultMap">
        SELECT <include refid="Select_Column_List"/>
        FROM t_industry_limit
        <include refid="Where_Clause"/>
        ORDER BY create_time DESC
    </select>

    <!-- 查询行业限制规则列表（包含条件和动作） -->
    <select id="selectIndustryLimitListWithDetails" resultMap="DetailVOResultMap">
        SELECT 
            r.id as rule_id,
            r.name as rule_name,
            r.code as rule_code,
            r.description as rule_description,
            r.status as rule_status,
            r.create_time as rule_create_time,
            r.update_time as rule_update_time,
            r.create_user as rule_create_user,
            r.update_user as rule_update_user,
            c.id as condition_id,
            c.field as condition_field,
            c.operator as condition_operator,
            c.value as condition_value,
            c.description as condition_description,
            c.sort_order as condition_sort_order,
            a.id as action_id,
            a.type as action_type,
            a.service_ids as action_service_ids,
            a.description as action_description,
            a.sort_order as action_sort_order
        FROM t_industry_limit r
        LEFT JOIN t_industry_limit_condition c ON r.id = c.rule_id
        LEFT JOIN t_industry_limit_action a ON r.id = a.rule_id
        <where>
            r.is_deleted = 0
            <if test="param != null">
                <if test="param.name != null and param.name != ''">
                    AND r.name LIKE CONCAT('%', #{param.name}, '%')
                </if>
                <if test="param.code != null and param.code != ''">
                    AND r.code LIKE CONCAT('%', #{param.code}, '%')
                </if>
                <if test="param.status != null">
                    AND r.status = #{param.status}
                </if>
                <if test="param.createUser != null and param.createUser != ''">
                    AND r.create_user = #{param.createUser}
                </if>
            </if>
        </where>
        ORDER BY r.create_time DESC, c.sort_order ASC, a.sort_order ASC
    </select>

    <!-- 根据ID查询行业限制规则详情 -->
    <select id="selectIndustryLimitById" parameterType="java.lang.Long" resultMap="VOResultMap">
        SELECT <include refid="Select_Column_List"/>
        FROM t_industry_limit
        WHERE id = #{id} AND is_deleted = 0
    </select>

    <!-- 检查规则编码是否存在 -->
    <select id="checkCodeExists" resultType="int">
        SELECT COUNT(1)
        FROM t_industry_limit
        WHERE code = #{code} AND is_deleted = 0
        <if test="id != null">
            AND id != #{id}
        </if>
    </select>

    <!-- 根据参数查询 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM t_industry_limit
        <include refid="Where_Clause"/>
        ORDER BY create_time DESC
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimit" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_industry_limit (
            name, code, description, status, create_time, update_time,
            create_user, update_user, tenant_id, is_deleted
        ) VALUES (
            #{name}, #{code}, #{description}, #{status}, #{createTime}, #{updateTime},
            #{createUser}, #{updateUser}, #{tenantId}, #{isDeleted}
        )
    </insert>

    <!-- 选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.kbao.kbcelms.industrylimit.entity.IndustryLimit">
        UPDATE t_industry_limit
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 逻辑删除 -->
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        UPDATE t_industry_limit SET is_deleted = 1 WHERE id = #{id}
    </update>

</mapper>
