<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kbc-elms</artifactId>
        <groupId>com.kbao</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.kbao</groupId>
    <artifactId>kbc-elms-service</artifactId>
    <name>kbc-elms-service</name>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencies>
        <!-- 关系型数据库配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>db-spring-boot-starter</artifactId>
        </dependency>
        <!-- 非关系型数据库配置 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>redis-spring-boot-starter</artifactId>
        </dependency>
        <!-- 日志中心 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>log-spring-boot-starter</artifactId>
        </dependency>
        <!-- 业务实体-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-elms-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bsc-web-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ums-web-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-tps-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ufs-web-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-ucs-web-client</artifactId>
        </dependency>
        <!--任务调度-->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-job-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>config-spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>common-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bpm-web-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bpm-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-bpm-entity</artifactId>
        </dependency>

        <!-- 企客生态服务 -->
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-espt-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kbao</groupId>
            <artifactId>kbc-espt-web-client</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
