package com.kbao.kbcelms.industrylimit.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.service.EnterpriseBasicInfoService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.industry.entity.Industry;
import com.kbao.kbcelms.industry.service.IndustryService;
import com.kbao.kbcelms.industrylimit.bean.EnterpriseServiceMatchResponse;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimit;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitAction;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimitCondition;
import com.kbao.tool.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 行业限制管理API服务实现类
 * <AUTHOR>
 * @date 2025-08-21
 */
@Slf4j
@Service
public class IndustryLimitApiService {

    @Autowired
    private IndustryLimitService industryLimitService;

    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private IndustryService industryService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据企业ID获取可启动的服务
     * @param enterpriseId 企业ID
     * @return 匹配结果
     */
    public EnterpriseServiceMatchResponse getEnterpriseServices(Integer enterpriseId) {
        log.info("开始处理企业服务匹配请求，企业ID: {}", enterpriseId);

        // 1. 获取企业信息
        GenAgentEnterprise agentEnterprise = genAgentEnterpriseService.selectByPrimaryKey(enterpriseId);
        if (agentEnterprise == null) {
            throw new RuntimeException("未找到企业信息，企业ID: " + enterpriseId);
        }

        // 2. 获取企业基本信息（通过统一社会信用代码）
        EnterpriseBasicInfo basicInfo = null;
        if (StringUtils.hasText(agentEnterprise.getCreditCode())) {
            basicInfo = enterpriseBasicInfoService.findByCreditCode(agentEnterprise.getCreditCode());
        }

        // 3. 获取行业信息
        Industry industry = null;
        if (StringUtils.hasText(agentEnterprise.getCategoryCode())) {
            industry = industryService.selectByCode(agentEnterprise.getCategoryCode());
        }

        // 4. 构建响应对象基本信息
        EnterpriseServiceMatchResponse response = buildBaseResponse(agentEnterprise, basicInfo, industry);

        // 5. 获取所有启用的行业限制规则
        List<IndustryLimit> rules = getEnabledIndustryLimitRules();
        if (CollectionUtils.isEmpty(rules)) {
            log.warn("未找到任何行业限制规则");
            response.setAvailableServiceIds(new ArrayList<>());
            response.setMatchedRules(new ArrayList<>());
            return response;
        }

        // 6. 执行规则匹配
        Set<String> applyServices = new HashSet<>();
        Set<String> noActionServices = new HashSet<>();

        for (IndustryLimit rule : rules) {
            EnterpriseServiceMatchResponse.MatchedRuleInfo matchResult = 
                    matchRule(rule, agentEnterprise);
            
            // 如果规则匹配成功，获取执行和不执行的服务ID
            if (matchResult.getMatched()) {
                Map<String, Set<String>> serviceMap = getServiceIdsByRuleIdWithType(rule.getId());
                applyServices.addAll(serviceMap.get("applyRule"));
                noActionServices.addAll(serviceMap.get("noAction"));
            }
        }

        // 处理冲突：如果同一个服务既在执行规则中又在不执行规则中，则该服务不可用
        Set<String> conflictServices = new HashSet<>(applyServices);
        conflictServices.retainAll(noActionServices);
        
        if (!conflictServices.isEmpty()) {
            log.info("发现冲突服务，将被排除: {}", conflictServices);
            applyServices.removeAll(conflictServices);
        }

        // 7. 设置响应结果
        response.setAvailableServiceIds(new ArrayList<>(applyServices));

        log.info("企业服务匹配完成，企业ID: {}, 可用服务数量: {}", 
                enterpriseId, applyServices.size());
        
        return response;
    }

    /**
     * 获取启用的行业限制规则
     */
    private List<IndustryLimit> getEnabledIndustryLimitRules() {
        try {
            // 构建查询条件
            IndustryLimit queryParam = new IndustryLimit();
            queryParam.setStatus(1);
            queryParam.setIsDeleted(0);
            return industryLimitService.selectByParam(MapUtils.objectToMap(queryParam));
        } catch (Exception e) {
            log.error("获取行业限制规则失败", e);
            throw new RuntimeException("获取行业限制规则失败: " + e.getMessage());
        }
    }

    /**
     * 构建基础响应信息
     */
    private EnterpriseServiceMatchResponse buildBaseResponse(
            GenAgentEnterprise agentEnterprise, 
            EnterpriseBasicInfo basicInfo, 
            Industry industry) {
        
        EnterpriseServiceMatchResponse response = new EnterpriseServiceMatchResponse();
        response.setEnterpriseId(agentEnterprise.getId().toString());
        response.setEnterpriseName(agentEnterprise.getName());
        response.setCreditCode(agentEnterprise.getCreditCode());
        
        if (industry != null) {
            response.setIndustryCode(industry.getCode());
            response.setIndustryName(industry.getName());
        }
        
        if (basicInfo != null) {
            response.setEnterpriseScale(basicInfo.getScale());
        }
        
        return response;
    }

    /**
     * 执行单个规则匹配
     */
    private EnterpriseServiceMatchResponse.MatchedRuleInfo matchRule(
            IndustryLimit rule, 
            GenAgentEnterprise agentEnterprise) {
        
        EnterpriseServiceMatchResponse.MatchedRuleInfo matchInfo = 
                new EnterpriseServiceMatchResponse.MatchedRuleInfo();
        
        matchInfo.setRuleId(rule.getId());
        matchInfo.setRuleName(rule.getName());
        matchInfo.setRuleCode(rule.getCode());
        matchInfo.setRuleDescription(rule.getDescription());
        
        try {
            // 1. 获取规则条件
            List<IndustryLimitCondition> conditions = industryLimitService.getConditionsByRuleId(rule.getId());
            
            // 2. 执行条件匹配
            boolean allConditionsMatch = true;
            StringBuilder matchDescription = new StringBuilder();
            
            if (!CollectionUtils.isEmpty(conditions)) {
                for (IndustryLimitCondition condition : conditions) {
                    boolean conditionMatch = evaluateCondition(condition, agentEnterprise);
                    if (!conditionMatch) {
                        allConditionsMatch = false;
                        matchDescription.append("条件不匹配: ").append(condition.getDescription()).append("; ");
                    }
                }
            }
            
            matchInfo.setMatched(allConditionsMatch);
            
            // 3. 如果匹配成功，获取可用服务
            if (allConditionsMatch) {
                List<String> serviceIds = getServiceIdsByRuleId(rule.getId());
                matchInfo.setServiceIds(serviceIds);
                matchDescription.append("匹配成功，可用服务数量: ").append(serviceIds.size());
            } else {
                matchInfo.setServiceIds(new ArrayList<>());
            }
            
            matchInfo.setMatchDescription(matchDescription.toString());
            
        } catch (Exception e) {
            log.error("规则匹配失败，规则ID: {}", rule.getId(), e);
            matchInfo.setMatched(false);
            matchInfo.setServiceIds(new ArrayList<>());
            matchInfo.setMatchDescription("规则匹配异常: " + e.getMessage());
        }
        
        return matchInfo;
    }

    /**
     * 评估单个条件
     */
    private boolean evaluateCondition(
            IndustryLimitCondition condition, 
            GenAgentEnterprise agentEnterprise) {
        
        String field = condition.getField();
        String operator = condition.getOperator();
        String expectedValue = condition.getValue();
        
        // 获取实际值
        Object actualValue = getFieldValue(field, agentEnterprise);
        
        if (actualValue == null) {
            return false;
        }
        
        // 根据操作符进行比较
        return compareValues(actualValue.toString(), expectedValue, operator);
    }

    /** 
     * 获取字段值 
     */ 
    private Object getFieldValue( 
            String field, 
            GenAgentEnterprise agentEnterprise) {
        if (agentEnterprise == null || StringUtils.isEmpty(field)) {
            return null;
        }
        
        try {
            // 使用反射获取字段值
            Field declaredField = GenAgentEnterprise.class.getDeclaredField(field);
            declaredField.setAccessible(true);
            return declaredField.get(agentEnterprise);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("获取字段值失败，字段名: {}, 错误信息: {}", field, e.getMessage());
            return null;
        }
    }

    /**
     * 比较值
     */
    private boolean compareValues(String actualValue, String expectedValue, String operator) {
        if (actualValue == null || expectedValue == null) {
            return false;
        }
        
        switch (operator) {
            case "eq": // 等于
                return actualValue.equals(expectedValue);
            case "ne": // 不等于
                return !actualValue.equals(expectedValue);
            case "contains": // 包含
                return actualValue.contains(expectedValue);
            case "gt": // 大于
                return compareNumeric(actualValue, expectedValue) > 0;
            case "lt": // 小于
                return compareNumeric(actualValue, expectedValue) < 0;
            case "gte": // 大于等于
                return compareNumeric(actualValue, expectedValue) >= 0;
            case "lte": // 小于等于
                return compareNumeric(actualValue, expectedValue) <= 0;
            case "range": // 区间
                return isInRange(actualValue, expectedValue);
            default:
                log.warn("未知操作符: {}", operator);
                return false;
        }
    }

    /**
     * 数值比较
     */
    private int compareNumeric(String actualValue, String expectedValue) {
        try {
            BigDecimal actual = new BigDecimal(actualValue);
            BigDecimal expected = new BigDecimal(expectedValue);
            return actual.compareTo(expected);
        } catch (NumberFormatException e) {
            // 如果不是数值，按字符串比较
            return actualValue.compareTo(expectedValue);
        }
    }

    /**
     * 检查是否在区间内
     */
    private boolean isInRange(String actualValue, String rangeValue) {
        try {
            // 期望格式: "min,max"
            String[] range = rangeValue.split(",");
            if (range.length != 2) {
                return false;
            }
            
            BigDecimal actual = new BigDecimal(actualValue);
            BigDecimal min = new BigDecimal(range[0].trim());
            BigDecimal max = new BigDecimal(range[1].trim());
            
            return actual.compareTo(min) >= 0 && actual.compareTo(max) <= 0;
        } catch (Exception e) {
            log.warn("区间比较失败: actualValue={}, rangeValue={}", actualValue, rangeValue, e);
            return false;
        }
    }

    /**
     * 根据规则ID获取服务ID列表
     */
    /**
     * 根据规则ID获取服务ID列表（区分执行和不执行）
     * @param ruleId 规则ID
     * @return 包含执行和不执行服务ID的Map
     */
    private Map<String, Set<String>> getServiceIdsByRuleIdWithType(Long ruleId) {
        Map<String, Set<String>> result = new HashMap<>();
        result.put("applyRule", new HashSet<>());
        result.put("noAction", new HashSet<>());
        
        try {
            List<IndustryLimitAction> actions = industryLimitService.getActionsByRuleId(ruleId);
            
            for (IndustryLimitAction action : actions) {
                if (StringUtils.hasText(action.getServiceIds()) && StringUtils.hasText(action.getType())) {
                    try {
                        List<String> actionServiceIds = objectMapper.readValue(
                                action.getServiceIds(), new TypeReference<List<String>>() {});
                        
                        Set<String> targetSet = result.get(action.getType());
                        if (targetSet != null) {
                            targetSet.addAll(actionServiceIds);
                        }
                    } catch (Exception e) {
                        log.warn("解析服务ID列表失败: {}", action.getServiceIds(), e);
                    }
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取服务ID列表失败，规则ID: {}", ruleId, e);
            return result;
        }
    }
    
    /**
     * 兼容性方法：获取执行类型的服务ID列表
     * @param ruleId 规则ID
     * @return 执行类型的服务ID列表
     */
    private List<String> getServiceIdsByRuleId(Long ruleId) {
        Map<String, Set<String>> serviceMap = getServiceIdsByRuleIdWithType(ruleId);
        return new ArrayList<>(serviceMap.get("applyRule"));
    }
}