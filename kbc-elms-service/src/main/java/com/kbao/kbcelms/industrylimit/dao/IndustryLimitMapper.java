package com.kbao.kbcelms.industrylimit.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.industrylimit.entity.IndustryLimit;
import com.kbao.kbcelms.industrylimit.vo.IndustryLimitVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行业限制规则 Mapper 接口
 * <AUTHOR>
 * @date 2025-08-08
 */
@Mapper
public interface IndustryLimitMapper extends BaseMapper<IndustryLimit, Long> {

    /**
     * 查询行业限制规则列表(含条件和动作)
     * @param param 查询参数
     * @return 规则列表
     */
    List<IndustryLimitVO> selectIndustryLimitList(@Param("param") Object param);

    /**
     * 查询行业限制规则列表（包含条件和动作详情，使用JOIN查询优化性能）
     * @param param 查询参数
     * @return 规则列表（包含条件和动作）
     */
    List<IndustryLimitVO> selectIndustryLimitListWithDetails(@Param("param") Object param);

    /**
     * 根据ID查询行业限制规则详情(含条件和动作)
     * @param id 规则ID
     * @return 规则详情
     */
    IndustryLimitVO selectIndustryLimitById(@Param("id") Long id);

    /**
     * 检查规则编码是否存在
     * @param code 规则编码
     * @param id 排除的ID（用于更新时检查）
     * @return 存在的数量
     */
    int checkCodeExists(@Param("code") String code, @Param("id") Long id);
}
