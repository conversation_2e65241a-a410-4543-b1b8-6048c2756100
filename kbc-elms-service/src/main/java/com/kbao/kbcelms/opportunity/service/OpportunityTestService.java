package com.kbao.kbcelms.opportunity.service;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.Result;
import com.kbao.feign.config.FeignRequestHeader;
import com.kbao.kbcelms.ucs.service.ElmsUcsClientService;
import com.kbao.kbcucs.agent.model.AgentBaseVO;
import com.kbao.kbcucs.client.model.BaseUserSensitiveInfo;
import com.kbao.kbcucs.client.model.GetByAgentCodeReq;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunity.vo.OpportunityCreateRequestVO;
import com.kbao.kbcelms.opportunity.vo.OpportunityCreateResponseVO;
import com.kbao.kbcelms.opportunitydetail.entity.OpportunityDetail;
import com.kbao.kbcelms.opportunitydetail.service.OpportunityDetailService;
import com.kbao.kbcelms.opportunityprocess.service.OpportunityProcessService;
import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;
import com.kbao.tool.util.DateUtils;
import com.kbao.tool.util.SysLoginUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 机会测试服务类
 * 包含新增机会等测试相关功能
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class OpportunityTestService {

    @Autowired
    private OpportunityService opportunityService;

    @Autowired
    private OpportunityDetailService opportunityDetailService;

    @Autowired
    private OpportunityProcessService opportunityProcessService;

    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private ElmsUcsClientService elmsUcsClientService;

    /**
     * 新增机会
     * @param request 新增机会请求
     * @return 新增机会响应
     */
    @Transactional(rollbackFor = Exception.class)
    public OpportunityCreateResponseVO createOpportunity(OpportunityCreateRequestVO request) {
        // 1. 根据agentCode获取agent信息
        AgentBaseVO agentInfo = getAgentInfoByCode(request.getAgentCode());
        
        // 2. 生成机会编码
        String bizCode = generateOpportunityBizCode();
        
        // 3. 创建机会实体
        Opportunity opportunity = new Opportunity();
        opportunity.setBizCode(bizCode);
        opportunity.setAgentCode(request.getAgentCode());
        opportunity.setAgentName(agentInfo.getAgentName());
        opportunity.setOpportunityName(request.getOpportunityName());
        opportunity.setAgentEnterpriseId(4); // 预设企业ID为4
        // 机会类型暂时设置为默认值，后续根据企业ID确定
        opportunity.setOpportunityType("1"); // 默认员工福利类型
        // industryCode不再设置，保持默认值
        opportunity.setStatus(1); // 设置为已提交状态
        opportunity.setIsDeleted(0);
        
        // 4. 设置机构相关信息
        if (agentInfo != null) {
            opportunity.setAreaCenterCode(agentInfo.getAreaCenterCode());
            opportunity.setAreaCenterName(agentInfo.getAreaCenterName());
            opportunity.setLegalCode(agentInfo.getLegalCode());
            opportunity.setLegalName(agentInfo.getLegalName());
            opportunity.setCompanyCode(agentInfo.getCompanyCode());
            opportunity.setCompanyName(agentInfo.getCompanyName());
            opportunity.setTradingCenterCode(agentInfo.getTradingCenterCode());
            opportunity.setTradingCenterName(agentInfo.getTradingCenterName());
            opportunity.setSalesCenterCode(agentInfo.getSalesCenterCode());
            opportunity.setSalesCenterName(agentInfo.getSalesCenterName());
            // 区域中心信息可能需要从其他字段获取，暂时设置为空
            opportunity.setAreaCenterCode(null);
            opportunity.setAreaCenterName(null);
        }
        
        // 5. 设置创建信息
        opportunity.setCreateId(SysLoginUtils.getUserId());
        opportunity.setCreateTime(new Date());
        opportunity.setUpdateId(SysLoginUtils.getUserId());
        opportunity.setUpdateTime(new Date());
        opportunity.setTenantId(SysLoginUtils.getUser().getTenantId());
        
        // 6. 保存机会
        opportunityService.insert(opportunity);
        
        // 7. 创建机会详情
        if (request.getInsureNum() != null || request.getPremiumBudget() != null || 
            request.getHasHistoryPolicy() != null || request.getContacter() != null) {
            createOpportunityDetail(opportunity.getId(), request);
        }
        
        // 8. 启动流程
        try {
            // 根据企业ID获取企业类型
            String companyType = determineCompanyType(opportunity.getAgentEnterpriseId());
            opportunityProcessService.startProcess(opportunity.getId(), companyType);
            log.info("机会流程启动成功，机会ID：{}，企业类型：{}", opportunity.getId(), companyType);
        } catch (Exception e) {
            log.error("启动机会流程失败，机会ID：{}，错误信息：{}", opportunity.getId(), e.getMessage(), e);
            // 流程启动失败不影响机会创建，只记录日志
        }
        
        // 9. 构建响应
        return buildCreateResponse(opportunity, agentInfo);
    }
    
    /**
     * 根据agentCode获取agent信息
     * @param agentCode agent编码
     * @return agent信息
     */
    private AgentBaseVO getAgentInfoByCode(String agentCode) {
        try {
            GetByAgentCodeReq req = new GetByAgentCodeReq();
            req.setAgentCode(agentCode);
            BaseUserSensitiveInfo info = new BaseUserSensitiveInfo();
            info.setReturnName(true);
            info.setReturnOrg(true);
            req.setInfo(info);
            
            // 设置租户ID到请求头
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("tenantId", SysLoginUtils.getUser().getTenantId());
            FeignRequestHeader.Header.set(headerMap);
            
            Result<AgentBaseVO> result = elmsUcsClientService.getAgentInfoV2(req);
            if (!ResultStatusEnum.isSuccess(result.getResp_code())) {
                throw new BusinessException("获取agent信息失败：" + result.getResp_msg());
            }
            return result.getDatas();
        } catch (Exception e) {
            log.error("获取agent信息异常，agentCode: {}, error: {}", agentCode, e.getMessage(), e);
            throw new BusinessException("获取agent信息异常：" + e.getMessage());
        }
    }
    
    /**
     * 生成机会编码
     * @return 机会编码
     */
    private String generateOpportunityBizCode() {
        String prefix = "OPP";
        String dateStr = DateUtils.dateTime2Str(new Date(), "yyyyMMdd");
        String randomStr = String.valueOf((int)(Math.random() * 10000));
        return prefix + dateStr + String.format("%04d", Integer.parseInt(randomStr));
    }
    
    /**
     * 创建机会详情
     * @param opportunityId 机会ID
     * @param request 请求参数
     */
    private void createOpportunityDetail(Integer opportunityId, OpportunityCreateRequestVO request) {
        OpportunityDetail detail = new OpportunityDetail();
        detail.setOpportunityId(opportunityId);
        detail.setInsureNum(request.getInsureNum());
        detail.setHasHistoryPolicy(request.getHasHistoryPolicy());
        detail.setPremiumBudget(request.getPremiumBudget());
        detail.setContacter(request.getContacter());
        detail.setContacterPost(request.getContacterPosition());
        // 注意：OpportunityDetail实体中没有contacterPhone字段，暂时不设置
        detail.setRemark(request.getRemark());
        detail.setTenantId(SysLoginUtils.getUser().getTenantId());
        detail.setIsDeleted(0);
        detail.setCreateId(SysLoginUtils.getUserId());
        detail.setCreateTime(new Date());
        detail.setUpdateId(SysLoginUtils.getUserId());
        detail.setUpdateTime(new Date());
        
        opportunityDetailService.insert(detail);
    }
    
    /**
     * 构建创建响应
     * @param opportunity 机会实体
     * @param agentInfo agent信息
     * @return 响应对象
     */
    private OpportunityCreateResponseVO buildCreateResponse(Opportunity opportunity, AgentBaseVO agentInfo) {
        OpportunityCreateResponseVO response = new OpportunityCreateResponseVO();
        response.setOpportunityId(opportunity.getId());
        response.setBizCode(opportunity.getBizCode());
        response.setOpportunityName(opportunity.getOpportunityName());
        response.setAgentCode(opportunity.getAgentCode());
        response.setAgentName(opportunity.getAgentName());
        response.setStatus(opportunity.getStatus());
        response.setAgentEnterpriseId(opportunity.getAgentEnterpriseId());
        response.setCreateTime(opportunity.getCreateTime());
        response.setAreaCenterCode(opportunity.getAreaCenterCode());
        response.setAreaCenterName(opportunity.getAreaCenterName());
        response.setLegalCode(opportunity.getLegalCode());
        response.setLegalName(opportunity.getLegalName());
        response.setCompanyCode(opportunity.getCompanyCode());
        response.setCompanyName(opportunity.getCompanyName());
        response.setTradingCenterCode(opportunity.getTradingCenterCode());
        response.setTradingCenterName(opportunity.getTradingCenterName());
        response.setSalesCenterCode(opportunity.getSalesCenterCode());
        response.setSalesCenterName(opportunity.getSalesCenterName());
        
        return response;
    }
    
    
    /**
     * 根据企业ID获取企业类型
     * @param agentEnterpriseId 企业ID
     * @return 企业类型
     */
    private String determineCompanyType(Integer agentEnterpriseId) {
        try {
            // 根据企业ID查询企业信息
            GenAgentEnterprise enterprise = genAgentEnterpriseService.selectByPrimaryKey(agentEnterpriseId);
            
            if (enterprise != null) {
                String dtType = enterprise.getDtType();
                log.info("根据企业ID: {} 获取到企业类型: {}", agentEnterpriseId, dtType);
                return dtType;
            }
            
            // 如果没有找到企业信息，使用默认类型
            log.warn("未找到企业ID: {} 对应的企业信息，使用默认企业类型", agentEnterpriseId);
            return "A";
            
        } catch (Exception e) {
            log.error("获取企业信息失败，企业ID: {}, error: {}", agentEnterpriseId, e.getMessage(), e);
            // 异常情况下使用默认类型
            return "A";
        }
    }
}
